'use client';

import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { X, RotateCcw, Loader2 } from 'lucide-react';
import { appTheme } from '@/app/theme';
import { processAIAssistantContent } from '@/utils/htmlSanitizer';

interface RegenerateMessageModalProps {
  isOpen: boolean;
  onClose: () => void;
  onRegenerate: (additionalContext?: string) => Promise<void>;
  originalMessage?: any;
  loading?: boolean;
}

const ModalOverlay = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: ${props => (props.$isOpen ? 'flex' : 'none')};
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
`;

const ModalContent = styled.div`
  background: ${appTheme.colors.background.main};
  border-radius: ${appTheme.borderRadius.lg};
  box-shadow: ${appTheme.shadows.lg};
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
`;

const ModalHeader = styled.div`
  padding: ${appTheme.spacing.lg};
  border-bottom: 1px solid ${appTheme.colors.border};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const ModalTitle = styled.h3`
  margin: 0;
  color: ${appTheme.colors.text.primary};
  font-size: ${appTheme.typography.fontSizes.lg};
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.sm};
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: ${appTheme.colors.text.secondary};
  cursor: pointer;
  padding: ${appTheme.spacing.xs};
  border-radius: ${appTheme.borderRadius.sm};
  transition: all 0.2s ease;

  &:hover {
    background: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
  }
`;

const ModalBody = styled.div`
  padding: ${appTheme.spacing.lg};
`;

const OriginalMessageSection = styled.div`
  margin-bottom: ${appTheme.spacing.lg};
`;

const SectionLabel = styled.label`
  display: block;
  margin-bottom: ${appTheme.spacing.sm};
  color: ${appTheme.colors.text.primary};
  font-weight: 500;
  font-size: ${appTheme.typography.fontSizes.sm};
`;

const OriginalMessagePreview = styled.div`
  background: ${appTheme.colors.background.main};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.lg};
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.md};
  max-height: 200px;
  overflow-y: auto;
  color: ${appTheme.colors.text.primary};
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;

  /* Ensure font family inheritance for all content */
  * {
    font-family: inherit;
  }

  /* HTML element styling for AI Assistant messages - exact copy from AssistantMessageBubble */
  /* Headings - consistent with RichTextEditor */
  h1, h2, h3, h4, h5, h6 {
    margin: 8px 0 4px 0;
    font-weight: 600;
    color: ${appTheme.colors.text.primary};
    line-height: 1.3;
  }

  h1 {
    font-size: 1.5em;
    margin: 12px 0 6px 0;
  }
  h2 {
    font-size: 1.3em;
    margin: 10px 0 5px 0;
  }
  h3 {
    font-size: 1.2em;
    margin: 8px 0 4px 0;
  }
  h4 {
    font-size: 1.1em;
    margin: 8px 0 4px 0;
  }
  h5 {
    font-size: 1.05em;
    margin: 6px 0 3px 0;
  }
  h6 {
    font-size: 1em;
    margin: 6px 0 3px 0;
    font-weight: 500;
  }

  /* Paragraphs */
  p {
    margin: ${appTheme.spacing.xs} 0;
    line-height: 1.5;
  }

  /* Lists */
  ul, ol {
    margin: ${appTheme.spacing.xs} 0;
    padding-left: ${appTheme.spacing.lg};
  }

  li {
    margin: 2px 0;
    line-height: 1.4;
  }

  /* Code blocks */
  pre {
    background: ${appTheme.colors.background.lighter};
    border: 1px solid ${appTheme.colors.border};
    border-radius: ${appTheme.borderRadius.sm};
    padding: ${appTheme.spacing.sm};
    margin: ${appTheme.spacing.xs} 0;
    overflow-x: auto;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.4;
  }

  /* Inline code */
  code {
    background: ${appTheme.colors.background.lighter};
    border: 1px solid ${appTheme.colors.border};
    border-radius: 3px;
    padding: 1px 4px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.9em;
    color: ${appTheme.colors.text.primary};
  }

  /* Code inside pre blocks shouldn't have additional styling */
  pre code {
    background: none;
    border: none;
    padding: 0;
    font-size: inherit;
  }

  /* Links */
  a {
    color: ${appTheme.colors.primary};
    text-decoration: underline;

    &:hover {
      color: ${appTheme.colors.primaryHover};
    }
  }

  /* Blockquotes */
  blockquote {
    border-left: 3px solid ${appTheme.colors.primary};
    padding-left: ${appTheme.spacing.sm};
    margin: ${appTheme.spacing.xs} 0;
    color: ${appTheme.colors.text.secondary};
    font-style: italic;
  }

  /* Tables */
  table {
    border-collapse: collapse;
    width: 100%;
    margin: ${appTheme.spacing.xs} 0;
    font-size: 13px;
  }

  th, td {
    border: 1px solid ${appTheme.colors.border};
    padding: 6px 8px;
    text-align: left;
  }

  th {
    background: ${appTheme.colors.background.lighter};
    font-weight: 600;
  }

  /* Strong and emphasis */
  strong, b {
    font-weight: 600;
  }

  em, i {
    font-style: italic;
  }

  /* Horizontal rule */
  hr {
    border: none;
    border-top: 1px solid ${appTheme.colors.border};
    margin: ${appTheme.spacing.md} 0;
  }

  /* Line breaks */
  br {
    line-height: 1.5;
  }

  /* Mention styling - consistent with other components */
  .mention[data-mention="true"] {
    background-color: #e3f2fd;
    color: #1976d2;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    user-select: none;

    &:hover {
      background-color: #bbdefb;
    }
  }

  span.mention {
    background-color: rgba(100, 111, 239, 0.5) !important;
    color: white !important;
    padding: 4px 8px !important;
    border-radius: 100px !important;
    font-weight: 700 !important;
    text-decoration: none !important;
    display: inline !important;
    font-size: 12px !important;
  }
`;

const ContextSection = styled.div`
  margin-bottom: ${appTheme.spacing.lg};
`;

const ContextTextarea = styled.textarea`
  width: 100%;
  min-height: 100px;
  padding: ${appTheme.spacing.md};
  border: 1px solid ${appTheme.colors.border};
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  color: ${appTheme.colors.text.primary};
  background: ${appTheme.colors.background.main};
  resize: vertical;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: ${appTheme.colors.primary};
    box-shadow: 0 0 0 3px ${appTheme.colors.primary}20;
  }

  &::placeholder {
    color: ${appTheme.colors.text.light};
  }
`;

const ModalFooter = styled.div`
  padding: ${appTheme.spacing.lg};
  border-top: 1px solid ${appTheme.colors.border};
  display: flex;
  gap: ${appTheme.spacing.md};
  justify-content: flex-end;
`;

const Button = styled.button<{ $variant?: 'primary' | 'secondary' }>`
  padding: ${appTheme.spacing.sm} ${appTheme.spacing.lg};
  border: none;
  border-radius: ${appTheme.borderRadius.md};
  font-size: ${appTheme.typography.fontSizes.sm};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: ${appTheme.spacing.xs};

  ${props => props.$variant === 'primary' ? `
    background: ${appTheme.colors.primary};
    color: white;

    &:hover:not(:disabled) {
      background: ${appTheme.colors.primaryHover};
    }

    &:disabled {
      background: ${appTheme.colors.text.light};
      cursor: not-allowed;
    }
  ` : `
    background: ${appTheme.colors.background.light};
    color: ${appTheme.colors.text.primary};
    border: 1px solid ${appTheme.colors.border};

    &:hover:not(:disabled) {
      background: ${appTheme.colors.background.lighter};
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  `}
`;

const HelpText = styled.p`
  margin: ${appTheme.spacing.sm} 0 0 0;
  color: ${appTheme.colors.text.light};
  font-size: ${appTheme.typography.fontSizes.xs};
  line-height: 1.4;
`;

export default function RegenerateMessageModal({
  isOpen,
  onClose,
  onRegenerate,
  originalMessage,
  loading = false,
}: RegenerateMessageModalProps) {
  const [additionalContext, setAdditionalContext] = useState('');

  // Reset context when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setAdditionalContext('');
    }
  }, [isOpen]);

  const handleRegenerate = async () => {
    try {
      await onRegenerate(additionalContext.trim() || undefined);
      onClose();
    } catch (error) {
      console.error('Error regenerating message:', error);
      // Error handling is done in the parent component
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape' && !loading) {
      onClose();
    }
  };

  // Get display content from original message
  const getDisplayContent = (message: any) => {
    if (!message) return '';
    
    try {
      const parsed = JSON.parse(message.content);
      return parsed.content || message.content;
    } catch {
      return message.content;
    }
  };

  return (
    <ModalOverlay $isOpen={isOpen} onClick={loading ? undefined : onClose} onKeyDown={handleKeyDown}>
      <ModalContent onClick={e => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>
            <RotateCcw size={20} />
            สร้างคำตอบ AI ใหม่
          </ModalTitle>
          <CloseButton onClick={onClose} disabled={loading}>
            <X size={20} />
          </CloseButton>
        </ModalHeader>

        <ModalBody>
          <OriginalMessageSection>
            <SectionLabel>คำตอบ AI เดิม:</SectionLabel>
            <OriginalMessagePreview
              dangerouslySetInnerHTML={{
                __html: processAIAssistantContent(getDisplayContent(originalMessage) || 'No content available')
              }}
            />
          </OriginalMessageSection>

          <ContextSection>
            <SectionLabel>บริบทเพิ่มเติม (เลือกได้):</SectionLabel>
            <ContextTextarea
              value={additionalContext}
              onChange={e => setAdditionalContext(e.target.value)}
              placeholder="ระบุบริบทเพิ่มเติม การแก้ไข หรือคำแนะนำเฉพาะสำหรับการสร้างคำตอบใหม่..."
              disabled={loading}
            />
            <HelpText>
              คุณสามารถระบุบริบทเพิ่มเติมเพื่อช่วยปรับปรุงคำตอบที่สร้างใหม่ หรือเว้นว่างไว้เพื่อสร้างใหม่ด้วยบริบทเดิม
            </HelpText>
          </ContextSection>
        </ModalBody>

        <ModalFooter>
          <Button $variant="secondary" onClick={onClose} disabled={loading}>
            ยกเลิก
          </Button>
          <Button $variant="primary" onClick={handleRegenerate} disabled={loading}>
            {loading ? (
              <>
                <Loader2 size={16} className="animate-spin" />
                กำลังสร้างใหม่...
              </>
            ) : (
              <>
                <RotateCcw size={16} />
                สร้างใหม่
              </>
            )}
          </Button>
        </ModalFooter>
      </ModalContent>
    </ModalOverlay>
  );
}
